<template>
  <router-view/>
  <common-dialog setting="smmd" v-model="personDialog">
    <div class="_fw q-pa-md bg-white">
      <q-tab-panels class="_panel" animated :model-value="!!confirmPerson._id">
        <q-tab-panel class="_panel" :name="false">
          <div class="q-pa-md font-1r tw-five">Hey, we found some people with your email. Any of these look familiar?</div>
          <q-list separator>
            <q-item v-for="(p, i) in pplOptions.data" :key="`p-${i}`" clickable @click="selectPerson(p)">
              <q-item-section avatar>
                <default-avatar :model-value="p"></default-avatar>
              </q-item-section>
              <q-item-section>
                <q-item-label>{{p.name}}</q-item-label>
                <q-item-label caption>E: {{p.email}} | P: {{p.phone?.number?.national || 'None'}} | A: {{p.address?.formatted || p.address?.city || 'None'}}</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </q-tab-panel>
        <q-tab-panel class="_panel" :name="true">
          <div class="text-center font-1r tw-six">Confirm this is you - we'll merge your account</div>
          <div class="row justify-center q-py-md">
            <div class="_fw mw500">
              <q-item>
                <q-item-section avatar>
                  <default-avatar :model-value="p"></default-avatar>
                </q-item-section>
                <q-item-section>
                  <q-item-label>{{confirmPerson.name}}</q-item-label>
                  <q-item-label caption>E: {{confirmPerson.email}} | P: {{confirmPerson.phone?.number?.national || 'None'}} | A: {{confirmPerson.address?.formatted || confirmPerson.address?.city || 'None'}}</q-item-label>
                </q-item-section>
              </q-item>

              <div class="q-pt-md row justify-around">
                <q-btn flat no-caps @click="confirmPerson = {}">
                  <span class="q-mr-sm">Not me</span>
                  <q-icon name="mdi-close" color="red"></q-icon>
                </q-btn>
                <q-btn flat no-caps @click="selectPerson(confirmPerson, true)">
                  <span class="q-mr-sm">Yes, that's me</span>
                  <q-icon name="mdi-check-circle" color="green"></q-icon>
                </q-btn>
              </div>
            </div>
          </div>
        </q-tab-panel>
      </q-tab-panels>

    </div>
  </common-dialog>

  <cc-vid-popup v-if="vidPopup" @done="clearVid" v-model:open="vidOpen"></cc-vid-popup>
</template>

<script setup>
  import DefaultAvatar from 'components/common/avatars/DefaultAvatar.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import CcVidPopup from 'components/utils/uploads/video-links/CcVidPopup.vue';

  import {onMounted, ref, watch} from 'vue';
  import {loginPerson} from 'stores/utils/login';
  import {useLogins} from 'stores/logins';
  import {parsePhoneNumber} from 'awesome-phonenumber';
  import {useEnvStore} from 'stores/env';
  const { login, pplStore } = loginPerson();
  const loginStore = useLogins()
  const envStore = useEnvStore();
  import {useRoute} from 'vue-router';
  import {SessionStorage} from 'symbol-auth-client';

  const route = useRoute();

  const pplOptions = ref({ total: 0, data: [] })
  const personDialog = ref(false);
  watch(login, async (nv) => {
    if(nv?._id && !nv.owner){
      const ppl = await pplStore.find({ query: { email: nv.email, login: { $exists: false } }})
      if(ppl?.total === 1) loginStore.patch(nv._id, { owner: ppl.data[0]._id })
      else if(ppl.total > 1) {
        pplOptions.value = ppl;
        personDialog.value = true;
      }
      else {
        const p = { email: nv.email, login: nv._id };
        if(nv.phone) p.phone = parsePhoneNumber(p.phone)
        if(nv.name) p.name = nv.name;
        const person = await pplStore.create(p);
        if(person) loginStore.patch(nv._id, { owner: person._id })
      }
    }
  }, { immediate: true })

  const confirmPerson = ref({})
  const selectPerson = async (p, confirm) => {
    if(!confirm) confirmPerson.value = p;
    else {
      confirmPerson.value = {};
      await loginStore.patch(login.value._id, { owner: p._id });
      personDialog.value = false;
      const newPerson = await pplStore.get(p._id)
      if(!newPerson.login) pplStore.patch(p._id, { login: login.value._id })
    }
  }

  const vidPopup = ref(false);
  const vidOpen = ref(false);

  const clearVid = () => {
    SessionStorage.removeItem('ccvideo');
    vidOpen.value = false;
    setTimeout(() => {
      vidPopup.value = false;
    }, 500)
  }
  onMounted(() => {
    console.log('ccvideo?', route.query)
    if(route.query.ccvideo) SessionStorage.setItem('ccvideo', route.query.ccvideo)
    if(SessionStorage.getItem('ccvideo')) {
      vidPopup.value = true;
      setTimeout(() => {
        vidOpen.value = true
      }, 5000)
    }
    // console.log('connecting logs off');
    const mql = window.matchMedia('(max-width: 1023px)');
    envStore.setMobile(mql.matches);
    mql.addEventListener('change', (e) => {
      if (e.matches) {
        envStore.setMobile(true);
      } else {
        envStore.setMobile(false);
      }
    });
  })
</script>
